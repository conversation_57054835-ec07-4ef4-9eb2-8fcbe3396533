# 🚀 Kubernetes Infrastructure Deployment

Bộ script tự động triển khai hạ tầng Kubernetes hoàn chỉnh với các dịch vụ cốt lõi:

- **🌐 Traefik** - API Gateway với HTTPS tự động và basic auth
- **🗄️ PostgreSQL** - Database với multi-environment support  
- **💾 MinIO** - Object Storage với bucket riêng cho từng môi trường
- **🔐 Keycloak** - Single Sign-On với realm và client được cấu hình sẵn

## ⚡ Quick Start

```bash
# Triển khai môi trường dev
make deploy-dev

# Kiểm tra trạng thái
make test-dev

# Xem credentials
make show-creds ENV=dev
```

📖 **[Xem hướng dẫn chi tiết](QUICKSTART.md)**

## 🏗️ Kiến trúc

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Development   │    │     Staging     │    │   Production    │
│   Environment   │    │   Environment   │    │   Environment   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
              ┌─────────────────────────────────────┐
              │        Shared Services              │
              │  ┌─────────────────────────────┐    │
              │  │         Traefik             │    │
              │  │      (API Gateway)          │    │
              │  └─────────────────────────────┘    │
              │  ┌─────────────────────────────┐    │
              │  │       PostgreSQL            │    │
              │  │   (dev/staging/prod DBs)    │    │
              │  └─────────────────────────────┘    │
              │  ┌─────────────────────────────┐    │
              │  │         MinIO               │    │
              │  │  (env-specific buckets)     │    │
              │  └─────────────────────────────┘    │
              │  ┌─────────────────────────────┐    │
              │  │        Keycloak             │    │
              │  │   (shared realm, env        │    │
              │  │    specific clients)        │    │
              │  └─────────────────────────────┘    │
              └─────────────────────────────────────┘
```

## 📋 Danh sách tài khoản quản trị

Sau khi triển khai, bạn sẽ nhận được file `credentials-{env}.txt` chứa:

### 🗄️ PostgreSQL
- **Host**: `postgres-service.default.svc.cluster.local:5432`
- **Username**: `admin`
- **Password**: `<tạo ngẫu nhiên 20 ký tự>`
- **Databases**: `dev`, `staging`, `prod`, `keycloak`

### 💾 MinIO
- **Access Key**: `admin`  
- **Secret Key**: `<tạo ngẫu nhiên 20 ký tự>`
- **Buckets**: `dev-bucket`, `staging-bucket`, `prod-bucket`

### 🔐 Keycloak
- **Username**: `admin`
- **Password**: `<tạo ngẫu nhiên 20 ký tự>`
- **Realm**: `shared-realm`
- **Clients**: `cms-client`, `api-client`, `frontend-client`

### 🌐 Traefik
- **Username**: `admin`
- **Password**: `<tạo ngẫu nhiên 16 ký tự>`

## 🔗 Địa chỉ dịch vụ

### Development
```
🌐 Traefik Dashboard: https://traefik-dev.yourdomain.com
💾 MinIO Console:     https://minio-dev.yourdomain.com
🔌 MinIO API:         https://minio-api-dev.yourdomain.com
🔐 Keycloak:          https://keycloak-dev.yourdomain.com
```

### Staging  
```
🌐 Traefik Dashboard: https://traefik-staging.yourdomain.com
💾 MinIO Console:     https://minio-staging.yourdomain.com
🔌 MinIO API:         https://minio-api-staging.yourdomain.com
🔐 Keycloak:          https://keycloak-staging.yourdomain.com
```

### Production
```
🌐 Traefik Dashboard: https://traefik.yourdomain.com
💾 MinIO Console:     https://minio.yourdomain.com  
🔌 MinIO API:         https://minio-api.yourdomain.com
🔐 Keycloak:          https://keycloak.yourdomain.com
```

## 🛠️ Các lệnh hữu ích

```bash
# Triển khai
make deploy ENV=dev          # Triển khai môi trường cụ thể
make deploy-staging          # Triển khai staging
make deploy-prod            # Triển khai production

# Kiểm tra
make test ENV=dev           # Test môi trường cụ thể  
make status                 # Hiển thị trạng thái cluster
make pods ENV=dev           # Hiển thị pods trong namespace

# Quản lý
make clean ENV=dev          # Xóa môi trường cụ thể
make clean-all             # Xóa tất cả môi trường
make update-creds ENV=dev   # Cập nhật credentials

# Debug
make logs-traefik          # Xem logs Traefik
make logs-postgres         # Xem logs PostgreSQL
make port-forward-postgres # Port forward PostgreSQL
```

## 📁 Cấu trúc project

```
k8s-deployment/
├── infrastructure/
│   ├── base/                    # Base deployments
│   ├── environments/            # Environment-specific configs
│   │   ├── dev/
│   │   ├── staging/
│   │   └── prod/
│   └── scripts/                 # Deployment scripts
├── cms/                         # CMS application (future)
├── Makefile                     # Quick commands
├── QUICKSTART.md               # Hướng dẫn nhanh
└── README.md                   # File này
```

## ⚙️ Yêu cầu hệ thống

- **Kubernetes cluster** (v1.20+)
- **kubectl** configured
- **PowerShell** (Windows) hoặc **pwsh** (Linux/macOS)
- **Domain name** với DNS pointing to your cluster

## 🔧 Cấu hình

1. **Cập nhật domain**: Thay `yourdomain.com` trong các file config
2. **Cập nhật email**: Thay email Let's Encrypt trong `base/traefik.yaml`
3. **Chạy deployment**: `make deploy-dev`

## 🆘 Hỗ trợ

- **📖 Documentation**: [infrastructure/README.md](infrastructure/README.md)
- **🚀 Quick Start**: [QUICKSTART.md](QUICKSTART.md)  
- **🐛 Issues**: Sử dụng `make logs-*` commands để debug

---

🎯 **Mục tiêu**: Triển khai hạ tầng Kubernetes production-ready trong vòng 5 phút!

# Jitsi Server Deployment on Kubernetes

## Tổng quan

Phương án triển khai Jitsi Meet Server trên Kubernetes cluster với các thành phần chính:
- **Jitsi Meet Web Interface**: Frontend cho người dùng
- **Jicofo (Jitsi Conference Focus)**: Điều phối hội nghị 
- **Jitsi Videobridge (JVB)**: Xử lý media streaming
- **Prosody XMPP Server**: Server XMPP cho signaling
- **PostgreSQL**: Database lưu trữ (tận dụng từ hạ tầng hiện có)

## Kiến trúc hệ thống

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Traefik       │    │   Jitsi Meet     │    │   Jicofo        │
│   (Load Balancer│────│   (Web UI)       │────│   (Coordinator) │
│   & SSL Term.)  │    │   Port: 80/443   │    │   Port: 8888    │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │                       │
         │              ┌─────────────────┐    ┌─────────────────┐
         │              │   Prosody       │    │   Videobridge   │
         └──────────────│   (XMPP Server) │────│   (Media Relay) │
                        │   Port: 5222    │    │   Port: 10000   │
                        └─────────────────┘    └─────────────────┘
                                 │
                        ┌─────────────────┐
                        │   PostgreSQL    │
                        │   (Database)    │
                        │   Port: 5432    │
                        └─────────────────┘
```

## Thành phần triển khai

### 1. **Jitsi Meet Web**
- **Image**: `jitsi/web:stable-8719`
- **Port**: 80/443
- **Function**: Giao diện web cho người dùng tham gia cuộc họp
- **Environment Variables**:
  - `JICOFO_COMPONENT_SECRET`
  - `JICOFO_AUTH_USER`
  - `XMPP_DOMAIN`
  - `XMPP_SERVER`

### 2. **Jicofo (Jitsi Conference Focus)**
- **Image**: `jitsi/jicofo:stable-8719`
- **Port**: 8888
- **Function**: Điều phối và quản lý các phiên hội nghị
- **Dependencies**: Prosody XMPP Server

### 3. **Jitsi Videobridge (JVB)**
- **Image**: `jitsi/jvb:stable-8719`
- **Port**: 10000 (UDP)
- **Function**: Xử lý và relay video/audio streams
- **Resources**: CPU intensive, cần memory cao

### 4. **Prosody XMPP Server**
- **Image**: `jitsi/prosody:stable-8719`
- **Port**: 5222, 5347
- **Function**: XMPP server cho signaling và authentication
- **Storage**: Cần persistent volume cho configs

### 5. **PostgreSQL** (Sử dụng từ hạ tầng hiện có)
- **Service**: `postgres-service.default.svc.cluster.local:5432`
- **Function**: Lưu trữ user sessions, room info, statistics

## Cấu hình Network

### Internal Services Communication
```yaml
# XMPP Communication
prosody-service:5222 ←→ jicofo:8888
prosody-service:5347 ←→ jvb:8080

# Web Interface
jitsi-web:80 ←→ prosody-service:5280
jitsi-web:80 ←→ jicofo:8888

# Database
prosody ←→ postgres-service:5432
```

### External Access
```yaml
# Traefik Ingress Routes
meet.example.com → jitsi-web:80
meet.example.com/xmpp-websocket → prosody:5280
```

## Resource Requirements

### Minimum Resources
```yaml
jitsi-web:
  requests: { cpu: "200m", memory: "512Mi" }
  limits: { cpu: "500m", memory: "1Gi" }

jicofo:
  requests: { cpu: "300m", memory: "768Mi" }
  limits: { cpu: "1000m", memory: "2Gi" }

jvb:
  requests: { cpu: "1000m", memory: "2Gi" }
  limits: { cpu: "2000m", memory: "4Gi" }

prosody:
  requests: { cpu: "200m", memory: "512Mi" }
  limits: { cpu: "500m", memory: "1Gi" }
```

### Storage Requirements
```yaml
prosody-config: 1Gi (RWO)
prosody-data: 5Gi (RWO)
jitsi-web-config: 1Gi (RWO)
```

## Environment Variables

### Jitsi Meet Web
```yaml
env:
  - name: XMPP_DOMAIN
    value: "meet.jitsi"
  - name: XMPP_SERVER
    value: "prosody-service"
  - name: JICOFO_AUTH_USER
    value: "focus"
  - name: XMPP_BOSH_URL_BASE
    value: "http://prosody-service:5280"
  - name: XMPP_AUTH_DOMAIN
    value: "auth.meet.jitsi"
  - name: XMPP_MUC_DOMAIN
    value: "muc.meet.jitsi"
  - name: TZ
    value: "Asia/Ho_Chi_Minh"
  - name: AMPLITUDE_ID
    value: ""
  - name: ANALYTICS_SCRIPT_URLS
    value: ""
  - name: ANALYTICS_WHITELISTED_EVENTS
    value: ""
```

### Jicofo
```yaml
env:
  - name: XMPP_SERVER
    value: "prosody-service"
  - name: XMPP_DOMAIN
    value: "meet.jitsi"
  - name: XMPP_AUTH_DOMAIN
    value: "auth.meet.jitsi"
  - name: XMPP_MUC_DOMAIN
    value: "muc.meet.jitsi"
  - name: JICOFO_COMPONENT_SECRET
    valueFrom:
      secretKeyRef:
        name: jitsi-secret
        key: jicofo-component-secret
  - name: JICOFO_AUTH_PASSWORD
    valueFrom:
      secretKeyRef:
        name: jitsi-secret
        key: jicofo-auth-password
  - name: TZ
    value: "Asia/Ho_Chi_Minh"
  - name: JVB_BREWERY_MUC
    value: "jvbbrewery"
```

### Jitsi Videobridge
```yaml
env:
  - name: XMPP_SERVER
    value: "prosody-service"
  - name: DOCKER_HOST_ADDRESS
    value: "your-public-ip"
  - name: XMPP_DOMAIN
    value: "meet.jitsi"
  - name: XMPP_AUTH_DOMAIN
    value: "auth.meet.jitsi"
  - name: XMPP_MUC_DOMAIN
    value: "muc.meet.jitsi"
  - name: JVB_AUTH_USER
    value: "jvb"
  - name: JVB_AUTH_PASSWORD
    valueFrom:
      secretKeyRef:
        name: jitsi-secret
        key: jvb-auth-password
  - name: JVB_BREWERY_MUC
    value: "jvbbrewery"
  - name: JVB_PORT
    value: "10000"
  - name: JVB_STUN_SERVERS
    value: "meet-jit-si-turnrelay.jitsi.net:443"
  - name: TZ
    value: "Asia/Ho_Chi_Minh"
```

### Prosody
```yaml
env:
  - name: XMPP_DOMAIN
    value: "meet.jitsi"
  - name: XMPP_AUTH_DOMAIN
    value: "auth.meet.jitsi"
  - name: XMPP_MUC_DOMAIN
    value: "muc.meet.jitsi"
  - name: JICOFO_COMPONENT_SECRET
    valueFrom:
      secretKeyRef:
        name: jitsi-secret
        key: jicofo-component-secret
  - name: JVB_AUTH_USER
    value: "jvb"
  - name: JVB_AUTH_PASSWORD
    valueFrom:
      secretKeyRef:
        name: jitsi-secret
        key: jvb-auth-password
  - name: JICOFO_AUTH_USER
    value: "focus"
  - name: JICOFO_AUTH_PASSWORD
    valueFrom:
      secretKeyRef:
        name: jitsi-secret
        key: jicofo-auth-password
  - name: TZ
    value: "Asia/Ho_Chi_Minh"
  - name: PUBLIC_URL
    value: "https://meet.example.com"
```

## Secrets Management

### Jitsi Secrets
```yaml
apiVersion: v1
kind: Secret
metadata:
  name: jitsi-secret
  namespace: default
type: Opaque
data:
  jicofo-component-secret: <base64-encoded-secret>
  jicofo-auth-password: <base64-encoded-password>
  jvb-auth-password: <base64-encoded-password>
```

## Networking & Security

### Service Mesh
- **ClusterIP Services**: Internal communication
- **NodePort/LoadBalancer**: External access via Traefik
- **Network Policies**: Restrict traffic between pods

### SSL/TLS
- **Traefik**: Automatic SSL termination
- **Let's Encrypt**: Auto-renewing certificates
- **HTTP → HTTPS**: Forced redirect

### Security Headers
```yaml
# Traefik Middleware
apiVersion: traefik.io/v1alpha1
kind: Middleware
metadata:
  name: jitsi-security-headers
spec:
  headers:
    customRequestHeaders:
      X-Forwarded-Proto: "https"
    customResponseHeaders:
      X-Frame-Options: "SAMEORIGIN"
      X-Content-Type-Options: "nosniff"
      Referrer-Policy: "strict-origin-when-cross-origin"
```

## Monitoring & Logging

### Metrics Collection
- **Prometheus**: Metrics từ JVB và Jicofo
- **Grafana**: Dashboard monitoring
- **AlertManager**: Cảnh báo khi có sự cố

### Log Aggregation
- **Fluentd/Fluent Bit**: Thu thập logs
- **Elasticsearch**: Lưu trữ logs
- **Kibana**: Phân tích logs

### Health Checks
```yaml
livenessProbe:
  httpGet:
    path: /about/health
    port: 8080
  initialDelaySeconds: 30
  periodSeconds: 30

readinessProbe:
  httpGet:
    path: /about/health
    port: 8080
  initialDelaySeconds: 10
  periodSeconds: 5
```

## Backup & Recovery

### Configuration Backup
- **Prosody configs**: Daily backup to persistent storage
- **SSL certificates**: Backup before renewal
- **Environment configs**: Git-based versioning

### Database Backup
- **PostgreSQL**: Tận dụng backup strategy hiện có
- **Room metadata**: Weekly backup
- **User sessions**: Real-time replication

## Scaling Strategy

### Horizontal Scaling
```yaml
# JVB Auto-scaling
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: jvb-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: jitsi-videobridge
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
```

### Load Distribution
- **JVB Clustering**: Multiple JVB instances
- **Geographic Distribution**: Multi-region deployment
- **Session Affinity**: Sticky sessions for ongoing meetings

## Deployment Steps

### Phase 1: Infrastructure Setup
1. **Namespace Creation**
2. **Secrets Generation**
3. **ConfigMaps Setup**
4. **Persistent Volume Claims**

### Phase 2: Core Services
1. **PostgreSQL** (sử dụng hiện có)
2. **Prosody XMPP Server**
3. **Jicofo Deployment**

### Phase 3: Media Services
1. **Jitsi Videobridge**
2. **JVB Auto-scaling Configuration**

### Phase 4: Web Interface
1. **Jitsi Meet Web**
2. **Traefik Ingress Routes**
3. **SSL Configuration**

### Phase 5: Monitoring & Security
1. **Monitoring Stack**
2. **Security Policies**
3. **Backup Procedures**

## Troubleshooting

### Common Issues
1. **JVB Connection Issues**: Check NAT/Firewall configs
2. **Authentication Failures**: Verify XMPP credentials
3. **High CPU Usage**: Scale JVB instances
4. **SSL Certificate Issues**: Check Traefik configurations

### Debug Commands
```bash
# Check pod status
kubectl get pods -n default -l app=jitsi

# Check logs
kubectl logs -f deployment/jitsi-videobridge -n default

# Check service connectivity
kubectl exec -it prosody-pod -- nc -zv jitsi-web-service 80

# Check XMPP connection
kubectl exec -it jicofo-pod -- telnet prosody-service 5222
```

## Performance Optimization

### JVB Tuning
```yaml
env:
  - name: VIDEOBRIDGE_MAX_MEMORY
    value: "3072m"
  - name: VIDEOBRIDGE_GC_TYPE
    value: "G1GC"
  - name: JVB_CONFIG_OVERRIDE
    value: |
      videobridge {
        stats {
          enabled = true
          interval = 5 seconds
        }
        apis {
          rest {
            enabled = true
          }
        }
      }
```

### Resource Limits Tuning
- **CPU**: Monitor usage patterns và adjust limits
- **Memory**: Set appropriate heap sizes
- **Network**: Optimize bandwidth allocation

## Integration với Hệ thống Hiện có

### Keycloak SSO Integration
- **OIDC Authentication**: Tích hợp với Keycloak realm
- **JWT Token Validation**: Secure room access
- **User Management**: Centralized via Keycloak

### Database Integration
- **Shared PostgreSQL**: Sử dụng chung database cluster
- **Connection Pooling**: Optimize database connections
- **Data Isolation**: Separate schemas cho Jitsi

### Monitoring Integration
- **Existing Prometheus**: Add Jitsi metrics targets
- **Grafana Dashboards**: Create custom dashboards
- **Alert Rules**: Integration với existing AlertManager

## Maintenance & Updates

### Rolling Updates
```yaml
strategy:
  type: RollingUpdate
  rollingUpdate:
    maxUnavailable: 1
    maxSurge: 1
```

### Version Upgrades
1. **Test Environment**: Deploy new version to staging
2. **Backup**: Full configuration backup
3. **Rolling Update**: Gradual upgrade across components
4. **Rollback Plan**: Quick revert strategy nếu có issues

### Security Updates
- **Regular Image Updates**: Monthly security patches
- **Vulnerability Scanning**: Automated security checks
- **Certificate Renewal**: Automated via Traefik

---

## Quick Start Commands

```bash
# 1. Clone repository
git clone <repository-url>
cd k8s-deployment

# 2. Switch to jitsi branch
git checkout jitsi-server-k8s

# 3. Deploy base infrastructure (nếu chưa có)
kubectl apply -f infrastructure/base/

# 4. Deploy jitsi components
kubectl apply -f infrastructure/base/jitsi/

# 5. Check deployment status
kubectl get pods -l app.kubernetes.io/name=jitsi

# 6. Access via browser
open https://meet.your-domain.com
```

## Support & Documentation

- **Official Jitsi Docs**: https://jitsi.github.io/handbook/
- **Kubernetes Docs**: https://kubernetes.io/docs/
- **Traefik Integration**: https://doc.traefik.io/traefik/
- **Internal Wiki**: [Link to internal documentation]

---

**Last Updated**: January 2025  
**Maintained by**: Infrastructure Team  
**Status**: Planning Phase