#!/bin/bash

# Jitsi Meet Deployment Script for Kubernetes
# Author: Infrastructure Team
# Date: January 2025

set -e

NAMESPACE="default"
DOMAIN="meet.example.com"

echo "🚀 Starting Jitsi Meet deployment on Kubernetes..."

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if kubectl is available
check_kubectl() {
    if ! command -v kubectl &> /dev/null; then
        print_error "kubectl is not installed or not in PATH"
        exit 1
    fi
    print_success "kubectl is available"
}

# Function to check cluster connectivity
check_cluster() {
    if ! kubectl cluster-info &> /dev/null; then
        print_error "Cannot connect to Kubernetes cluster"
        exit 1
    fi
    print_success "Connected to Kubernetes cluster"
}

# Function to create namespace if it doesn't exist
create_namespace() {
    if kubectl get namespace "$NAMESPACE" &> /dev/null; then
        print_warning "Namespace $NAMESPACE already exists"
    else
        kubectl create namespace "$NAMESPACE"
        print_success "Created namespace: $NAMESPACE"
    fi
}

# Function to apply manifests
apply_manifests() {
    local manifest_dir="$1"
    local description="$2"
    
    print_status "Deploying $description..."
    
    if [ -d "$manifest_dir" ]; then
        for file in "$manifest_dir"/*.yaml; do
            if [ -f "$file" ]; then
                print_status "Applying $(basename "$file")..."
                kubectl apply -f "$file" -n "$NAMESPACE"
            fi
        done
        print_success "$description deployed successfully"
    else
        print_error "Directory $manifest_dir not found"
        exit 1
    fi
}

# Function to wait for deployment
wait_for_deployment() {
    local deployment="$1"
    local timeout="$2"
    
    print_status "Waiting for $deployment to be ready..."
    
    if kubectl wait --for=condition=available --timeout="${timeout}s" deployment/"$deployment" -n "$NAMESPACE"; then
        print_success "$deployment is ready"
    else
        print_error "$deployment failed to become ready within ${timeout}s"
        return 1
    fi
}

# Function to check pod status
check_pod_status() {
    print_status "Checking pod status..."
    kubectl get pods -l app.kubernetes.io/name=jitsi -n "$NAMESPACE"
}

# Function to display service endpoints
show_endpoints() {
    print_status "Service endpoints:"
    kubectl get services -l app.kubernetes.io/name=jitsi -n "$NAMESPACE"
    
    print_status "Ingress information:"
    kubectl get ingressroute -l app.kubernetes.io/name=jitsi -n "$NAMESPACE" 2>/dev/null || echo "No Traefik IngressRoutes found"
}

# Function to generate random passwords if needed
generate_secrets() {
    print_status "Checking if secrets exist..."
    
    if kubectl get secret jitsi-secret -n "$NAMESPACE" &> /dev/null; then
        print_warning "jitsi-secret already exists, skipping generation"
    else
        print_status "Generating new secrets..."
        
        # Generate random passwords
        JICOFO_COMPONENT_SECRET=$(openssl rand -base64 32)
        JICOFO_AUTH_PASSWORD=$(openssl rand -base64 32)
        JVB_AUTH_PASSWORD=$(openssl rand -base64 32)
        PROSODY_ADMIN_PASSWORD=$(openssl rand -base64 32)
        
        # Create secret
        kubectl create secret generic jitsi-secret \
            --from-literal=jicofo-component-secret="$JICOFO_COMPONENT_SECRET" \
            --from-literal=jicofo-auth-password="$JICOFO_AUTH_PASSWORD" \
            --from-literal=jvb-auth-password="$JVB_AUTH_PASSWORD" \
            --from-literal=prosody-admin-password="$PROSODY_ADMIN_PASSWORD" \
            -n "$NAMESPACE"
            
        print_success "Generated and created jitsi-secret"
    fi
}

# Function to update domain in manifests
update_domain() {
    if [ "$DOMAIN" != "meet.example.com" ]; then
        print_status "Updating domain from meet.example.com to $DOMAIN..."
        find infrastructure/base/jitsi/ -name "*.yaml" -exec sed -i "s/meet\.example\.com/$DOMAIN/g" {} \;
        print_success "Domain updated in all manifests"
    fi
}

# Function to cleanup (for rollback)
cleanup() {
    print_warning "Cleaning up Jitsi deployment..."
    kubectl delete -f infrastructure/base/jitsi/ -n "$NAMESPACE" --ignore-not-found=true
    print_success "Cleanup completed"
}

# Function to show logs
show_logs() {
    print_status "Recent logs from Jitsi components:"
    
    echo -e "\n${YELLOW}=== Prosody Logs ===${NC}"
    kubectl logs -l app.kubernetes.io/component=prosody -n "$NAMESPACE" --tail=10 || true
    
    echo -e "\n${YELLOW}=== Jicofo Logs ===${NC}"
    kubectl logs -l app.kubernetes.io/component=jicofo -n "$NAMESPACE" --tail=10 || true
    
    echo -e "\n${YELLOW}=== JVB Logs ===${NC}"
    kubectl logs -l app.kubernetes.io/component=jvb -n "$NAMESPACE" --tail=10 || true
    
    echo -e "\n${YELLOW}=== Web Logs ===${NC}"
    kubectl logs -l app.kubernetes.io/component=web -n "$NAMESPACE" --tail=10 || true
}

# Main deployment function
main() {
    echo "================================================"
    echo "🎥 Jitsi Meet Kubernetes Deployment Script"
    echo "================================================"
    
    # Pre-flight checks
    check_kubectl
    check_cluster
    
    # Handle command line arguments
    case "${1:-deploy}" in
        "deploy")
            print_status "Starting deployment process..."
            
            # Update domain if specified
            if [ -n "$2" ]; then
                DOMAIN="$2"
                update_domain
            fi
            
            create_namespace
            generate_secrets
            
            # Deploy in order
            apply_manifests "infrastructure/base/jitsi" "Jitsi Meet components"
            
            # Wait for deployments
            print_status "Waiting for deployments to be ready..."
            wait_for_deployment "prosody" 300
            wait_for_deployment "jicofo" 300
            wait_for_deployment "jitsi-videobridge" 300
            wait_for_deployment "jitsi-web" 300
            
            check_pod_status
            show_endpoints
            
            echo "================================================"
            print_success "🎉 Jitsi Meet deployment completed!"
            echo "================================================"
            print_status "Access your Jitsi Meet instance at: https://$DOMAIN"
            print_status "Run '$0 status' to check the deployment status"
            print_status "Run '$0 logs' to view component logs"
            ;;
            
        "status")
            print_status "Checking Jitsi Meet deployment status..."
            check_pod_status
            show_endpoints
            ;;
            
        "logs")
            show_logs
            ;;
            
        "cleanup")
            read -p "Are you sure you want to delete the Jitsi deployment? (y/N): " confirm
            if [[ $confirm == [yY] || $confirm == [yY][eE][sS] ]]; then
                cleanup
            else
                print_status "Cleanup cancelled"
            fi
            ;;
            
        "help")
            echo "Usage: $0 [command] [domain]"
            echo ""
            echo "Commands:"
            echo "  deploy [domain]  - Deploy Jitsi Meet (default: meet.example.com)"
            echo "  status          - Show deployment status"
            echo "  logs            - Show component logs"
            echo "  cleanup         - Remove Jitsi deployment"
            echo "  help            - Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0 deploy meet.mycompany.com"
            echo "  $0 status"
            echo "  $0 logs"
            ;;
            
        *)
            print_error "Unknown command: $1"
            echo "Run '$0 help' for usage information"
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
