apiVersion: apps/v1
kind: Deployment
metadata:
  name: jitsi-videobridge
  namespace: jitsi-server
  labels:
    app.kubernetes.io/name: jitsi
    app.kubernetes.io/component: jvb
spec:
  replicas: 2
  selector:
    matchLabels:
      app.kubernetes.io/name: jitsi
      app.kubernetes.io/component: jvb
  template:
    metadata:
      labels:
        app.kubernetes.io/name: jitsi
        app.kubernetes.io/component: jvb
    spec:
      containers:
        - name: jvb
          image: jitsi/jvb:stable-8138
          ports:
            - containerPort: 8080
              name: http
            - containerPort: 10000
              name: rtp
              protocol: UDP
          env:
            - name: XMPP_SERVER
              value: "prosody-service"
            - name: DOCKER_HOST_ADDRESS
              value: "AUTO"  # Will be replaced with actual public IP
            - name: XMPP_DOMAIN
              value: "meet.jitsi"
            - name: XMPP_AUTH_DOMAIN
              value: "meet.jitsi"  # Use anonymous domain
            - name: XMPP_MUC_DOMAIN
              value: "muc.meet.jitsi"
            - name: XMPP_INTERNAL_MUC_DOMAIN
              value: "internal-muc.meet.jitsi"
            # Enable authentication with proper credentials
            - name: JVB_AUTH_USER
              value: "jvb"
            - name: JVB_AUTH_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: jitsi-secret
                  key: jvb-auth-password
            - name: JVB_BREWERY_MUC
              value: "jvbbrewery"
            - name: JVB_PORT
              value: "10000"
            - name: JVB_STUN_SERVERS
              value: "meet-jit-si-turnrelay.jitsi.net:443"
            - name: TZ
              value: "Asia/Ho_Chi_Minh"
            # XMPP Connection Settings  
            - name: XMPP_PORT
              value: "5222"
            # Enable TLS for secure communication
            - name: JVB_XMPP_DISABLE_TLS
              value: "false"
            - name: XMPP_USE_TLS
              value: "true"
            - name: XMPP_START_TLS
              value: "false"
            - name: JVB_DISABLE_STUN
              value: "false"
            # Additional TLS disable options from image
            - name: JVB_XMPP_ENABLE_TLS
              value: "false"
            - name: JVB_XMPP_CONNECT_TLS
              value: "false"
            # Force security mode to disabled
            - name: XMPP_SECURITY_MODE
              value: "disabled"
            - name: JVB_XMPP_SECURITY_MODE
              value: "disabled"
            # Override Smack library behavior
            - name: SMACK_DEBUG
              value: "false"
            # Force plain text connection
            - name: XMPP_REQUIRE_ENCRYPTION
              value: "false"
            - name: JVB_XMPP_REQUIRE_ENCRYPTION
              value: "false"
            # BOSH URL for web interface
            - name: XMPP_BOSH_URL_BASE
              value: "http://prosody-service:5280"
            - name: JVB_ENABLE_APIS
              value: "rest,colibri"
            - name: JVB_WS_DOMAIN
              value: "jitsi.osp.vn"
            - name: JVB_WS_SERVER_ID
              value: "default-id"
            - name: PUBLIC_URL
              value: "https://jitsi.osp.vn"
            # Performance tuning
            - name: VIDEOBRIDGE_MAX_MEMORY
              value: "3072m"
            - name: VIDEOBRIDGE_GC_TYPE
              value: "G1GC"
            - name: JVB_OPTS
              value: "-XX:+UseG1GC -XX:+UseStringDeduplication"
          resources:
            requests:
              memory: "2Gi"
              cpu: "1000m"
            limits:
              memory: "4Gi"
              cpu: "2000m"
          # Disable health checks for debugging
          # livenessProbe:
          #   httpGet:
          #     path: /about/health
          #     port: 8080
          #   initialDelaySeconds: 60
          #   periodSeconds: 30
          #   timeoutSeconds: 10
          # readinessProbe:
          #   httpGet:
          #     path: /about/health
          #     port: 8080
          #   initialDelaySeconds: 30
          #   periodSeconds: 10
          #   timeoutSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: jvb-service
  namespace: jitsi-server
  labels:
    app.kubernetes.io/name: jitsi
    app.kubernetes.io/component: jvb
spec:
  ports:
    - port: 8080
      targetPort: 8080
      name: http
    - port: 10000
      targetPort: 10000
      name: rtp
      protocol: UDP
  selector:
    app.kubernetes.io/name: jitsi
    app.kubernetes.io/component: jvb
  type: ClusterIP
---
# Service for external UDP traffic (NodePort or LoadBalancer)
apiVersion: v1
kind: Service
metadata:
  name: jvb-udp-service
  namespace: jitsi-server
  labels:
    app.kubernetes.io/name: jitsi
    app.kubernetes.io/component: jvb
spec:
  ports:
    - port: 10000
      targetPort: 10000
      nodePort: 30000
      name: rtp
      protocol: UDP
  selector:
    app.kubernetes.io/name: jitsi
    app.kubernetes.io/component: jvb
  type: NodePort
