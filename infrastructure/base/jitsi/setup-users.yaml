apiVersion: batch/v1
kind: Job
metadata:
  name: prosody-setup-users
  namespace: jitsi-server
  labels:
    app.kubernetes.io/name: jitsi
    app.kubernetes.io/component: setup
spec:
  template:
    spec:
      containers:
      - name: prosody-setup
        image: prosody/prosody:0.12
        command: ["/bin/bash"]
        args:
          - -c
          - |
            set -e
            echo "Waiting for Prosody to be ready..."
            sleep 30
            
            echo "Creating users..."
            # Connect to Prosody service and create users
            prosodyctl --config=/etc/prosody/prosody.cfg.lua register focus auth.meet.jitsi focuspassword || true
            prosodyctl --config=/etc/prosody/prosody.cfg.lua register jvb auth.meet.jitsi jvbpassword || true
            
            echo "Users created successfully"
        env:
        - name: PROSODY_SERVER
          value: "prosody-service"
        - name: XMPP_DOMAIN
          value: "meet.jitsi"
        - name: XMPP_AUTH_DOMAIN
          value: "auth.meet.jitsi"
        volumeMounts:
        - name: prosody-config
          mountPath: /etc/prosody/prosody.cfg.lua
          subPath: prosody.cfg.lua
      volumes:
      - name: prosody-config
        configMap:
          name: prosody-config
          items:
          - key: prosody.cfg.lua
            path: prosody.cfg.lua
      restartPolicy: OnFailure
  backoffLimit: 3
